import {
  CioConfig,
  CioLogLevel,
  CioRegion,
  CustomerIO,
  PushClickBehaviorAndroid,
} from 'customerio-reactnative';
import Constants from 'expo-constants';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useRef, useState } from 'react';
import { BackHandler, Linking, Platform, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';


// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

function handleRegistrationError(errorMessage: string) {
  console.error(errorMessage);
  throw new Error(errorMessage);
}

async function registerForPushNotificationsAsync() {
  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    if (finalStatus !== 'granted') {
      handleRegistrationError('Permission not granted to get push token for push notification!');
      return;
    }
    const projectId = Constants?.expoConfig?.extra?.eas?.projectId ?? Constants?.easConfig?.projectId;

    if (!projectId) {
      handleRegistrationError('Project ID not found');
    }
    try {
      const pushTokenString = (
        await Notifications.getExpoPushTokenAsync({
          projectId,
        })
      ).data;
      console.log('Push token:', pushTokenString);
      return pushTokenString;
    } catch (e: unknown) {
      handleRegistrationError(`${e}`);
    }
  } else {
    handleRegistrationError('Must use physical device for push notifications');
  }
}

export default function RootLayout() {
  const [userAgent, setUserAgent] = useState<string>('');
  const [expoPushToken, setExpoPushToken] = useState('');
  const [notification, setNotification] = useState<Notifications.Notification | undefined>(undefined);
  const [canGoBack, setCanGoBack] = useState(false);
  const webViewRef = useRef<WebView>(null);

  useEffect(() => {
    const generateUserAgent = () => {
      const deviceModel = Device.modelName || 'Unknown Device';
      const osVersion = Device.osVersion || '16.0';

      if (Platform.OS === 'ios') {
        return `Mozilla/5.0 (iPhone; CPU iPhone OS ${osVersion.replace('.', '_')} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/${osVersion} Mobile/15E148 Safari/604.1`;
      } else {
        return `Mozilla/5.0 (Linux; Android ${osVersion}; ${deviceModel}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36`;
      }
    };

    setUserAgent(generateUserAgent());

    // Register for push notifications
    const getToken = async () => {

      const config: CioConfig = {
        cdpApiKey: 'cc4bfa2af7cf0b31b92d', // Mandatory
        migrationSiteId: 'bfad1ca1163195d30f7d', // Required if migrating from an earlier version
        region: CioRegion.US,
        logLevel: CioLogLevel.Debug,
        trackApplicationLifecycleEvents: true,
        inApp: {
          siteId: 'bfad1ca1163195d30f7d',
        },
        push: {
          android: {
            pushClickBehavior: PushClickBehaviorAndroid.ActivityPreventRestart
          }
        }
      };
      CustomerIO.initialize(config)

      var options = { "ios": { "sound": true, "badge": true } }
      const res = await CustomerIO.pushMessaging.showPromptForPushNotifications(options);
      if (res === "GRANTED") {
        // console.log("coming here");

        // const t = await CustomerIO.pushMessaging.getRegisteredDeviceToken();

        // console.log(t, 't');

        // setExpoPushToken(t);
        console.log("Permission granted, getting token...");

        try {
          // await CustomerIO.identify({
          //   "userId": "8ad00b000001",
          //   "traits": {
          //     "name": "Kaushik",
          //     "email": "<EMAIL>"
          //   }
          // });
          const t = await CustomerIO.pushMessaging.getRegisteredDeviceToken();
          console.log("Raw token response:", t);
          console.log("Token type:", typeof t);
          console.log("Token length:", t?.length);

          if (t) {
            console.log("Token successfully retrieved:", t);
            setExpoPushToken(t);
          } else {
            console.log("Token is null, undefined, or empty");
          }
        } catch (error) {
          console.error("Error getting device token:", error);
        }
      }


    }

    console.log("coming after token");

    getToken();

    // registerForPushNotificationsAsync()
    //   .then(token => setExpoPushToken(token ?? ''))
    //   .catch((error: any) => {
    //     console.error('Push notification registration failed:', error);
    //     setExpoPushToken('');
    //   });

    // Set up notification listeners
    // const notificationListener = Notifications.addNotificationReceivedListener(notification => {
    //   setNotification(notification);
    //   console.log('Notification received:', notification);
    // });

    // const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
    //   console.log('Notification response:', response);
    //   // You can handle notification taps here, e.g., navigate to specific pages
    // });

    // return () => {
    //   notificationListener.remove();
    //   responseListener.remove();
    // };
  }, []);

  // Handle hardware back button (Android) and enable back navigation
  useEffect(() => {
    const backAction = () => {
      if (canGoBack && webViewRef.current) {
        webViewRef.current.goBack();
        return true; // Prevent default behavior
      }
      return false; // Allow default behavior (exit app)
    };

    if (Platform.OS === 'android') {
      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
      return () => backHandler.remove();
    }
  }, [canGoBack]);

  // Inject push token and enable iOS interactions
  const injectedJavaScript = `
    // Store push token globally
    window.expoPushToken = "${expoPushToken}";

    // Function to send push token to React app
    function sendPushTokenToReact() {
      if (window.expoPushToken) {
        // Store in localStorage for persistence
        localStorage.setItem('expoPushToken', window.expoPushToken);

        // Dispatch custom event for immediate handling
        window.dispatchEvent(new CustomEvent('expoPushTokenReceived', {
          detail: { token: window.expoPushToken }
        }));

        console.log('Push token sent to React app:', window.expoPushToken);
      }
    }

    // Send token immediately when script loads
    sendPushTokenToReact();

    // Listen for requests from React app
    window.addEventListener('message', function(event) {
      if (event.data.type === 'GET_PUSH_TOKEN') {
        window.postMessage({type: 'PUSH_TOKEN', token: window.expoPushToken}, '*');
      }
    });

    // Enable iOS back gesture and touch interactions
    document.addEventListener('DOMContentLoaded', function() {
      // Send token again after DOM is loaded
      sendPushTokenToReact();

      // Prevent default touch behaviors that might interfere with native gestures
      document.body.style.webkitTouchCallout = 'none';
      document.body.style.webkitUserSelect = 'none';
      document.body.style.userSelect = 'none';

      // Enable momentum scrolling for iOS
      document.body.style.webkitOverflowScrolling = 'touch';

      // Add meta viewport for better mobile experience
      if (!document.querySelector('meta[name="viewport"]')) {
        var meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
        document.getElementsByTagName('head')[0].appendChild(meta);
      }
    });

    true; // Required for iOS
  `;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <WebView
        ref={webViewRef}
        source={{ uri: 'https://app.emergent.sh?mobile=true' }}
        style={styles.webview}
        userAgent={userAgent}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        scalesPageToFit={true}
        injectedJavaScript={injectedJavaScript}
        // Enable shared cookies with Safari (iOS only)
        sharedCookiesEnabled={true}
        // Enable third-party cookies for OAuth providers
        thirdPartyCookiesEnabled={true}
        // Allow mixed content for better compatibility
        mixedContentMode="compatibility"
        // Store data locally in WebView
        cacheEnabled={true}
        // Enable iOS native interactions
        allowsBackForwardNavigationGestures={true}
        bounces={true}
        scrollEnabled={true}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        // Enable pull-to-refresh
        pullToRefreshEnabled={true}
        // Handle navigation state changes
        onNavigationStateChange={(navState) => {
          setCanGoBack(navState.canGoBack);
        }}
        onMessage={(event) => {
          try {
            const data = JSON.parse(event.nativeEvent.data);
            console.log('Message from WebView:', data);

            // Handle different message types from the web app
            switch (data.type) {
              case 'REQUEST_PUSH_TOKEN':
                // Send push token back to web app
                if (webViewRef.current && expoPushToken) {
                  webViewRef.current.postMessage(JSON.stringify({
                    type: 'PUSH_TOKEN_RESPONSE',
                    token: expoPushToken
                  }));
                }
                break;
              case 'OPEN_EXTERNAL_URL':
                Linking.openURL(data.url);
                break;
              case 'PUSH_TOKEN_RECEIVED':
                console.log('Web app confirmed push token receipt:', data.token);
                break;
              default:
                console.log('Unknown message type:', data.type);
            }
          } catch (error) {
            console.error('Error parsing message from WebView:', error);
          }
        }}
        // Additional iOS-specific props
        {...(Platform.OS === 'ios' && {
          allowsInlineMediaPlayback: true,
          mediaPlaybackRequiresUserAction: false,
          automaticallyAdjustContentInsets: false,
          contentInsetAdjustmentBehavior: 'never',
        })}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
  },
});
